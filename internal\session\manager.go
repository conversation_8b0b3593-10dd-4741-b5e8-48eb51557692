package session

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"arien-ai/pkg/types"
)

// Manager handles session persistence and management
type Manager struct {
	sessionDir string
	maxSessions int
}

// NewManager creates a new session manager
func NewManager(sessionDir string, maxSessions int) (*Manager, error) {
	// Ensure session directory exists
	if err := os.Mkdir<PERSON>ll(sessionDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create session directory: %w", err)
	}

	return &Manager{
		sessionDir:  sessionDir,
		maxSessions: maxSessions,
	}, nil
}

// SaveSession saves a session to disk
func (m *Manager) SaveSession(session *types.Session) error {
	if session == nil {
		return fmt.Errorf("session cannot be nil")
	}

	// Update session metadata
	session.UpdatedAt = time.Now()
	session.LastActivity = time.Now()

	// Create session file path
	filename := fmt.Sprintf("%s.json", session.ID)
	filepath := filepath.Join(m.sessionDir, filename)

	// Marshal session to JSON
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session: %w", err)
	}

	// Write to file
	if err := os.WriteFile(filepath, data, 0644); err != nil {
		return fmt.Errorf("failed to write session file: %w", err)
	}

	// Clean up old sessions if needed
	if err := m.cleanupOldSessions(); err != nil {
		// Log warning but don't fail the save operation
		fmt.Printf("Warning: failed to cleanup old sessions: %v\n", err)
	}

	return nil
}

// LoadSession loads a session from disk
func (m *Manager) LoadSession(sessionID string) (*types.Session, error) {
	if sessionID == "" {
		return nil, fmt.Errorf("session ID cannot be empty")
	}

	// Create session file path
	filename := fmt.Sprintf("%s.json", sessionID)
	filepath := filepath.Join(m.sessionDir, filename)

	// Check if file exists
	if _, err := os.Stat(filepath); os.IsNotExist(err) {
		return nil, fmt.Errorf("session not found: %s", sessionID)
	}

	// Read file
	data, err := os.ReadFile(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to read session file: %w", err)
	}

	// Unmarshal session
	var session types.Session
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("failed to unmarshal session: %w", err)
	}

	return &session, nil
}

// DeleteSession deletes a session from disk
func (m *Manager) DeleteSession(sessionID string) error {
	if sessionID == "" {
		return fmt.Errorf("session ID cannot be empty")
	}

	// Create session file path
	filename := fmt.Sprintf("%s.json", sessionID)
	filepath := filepath.Join(m.sessionDir, filename)

	// Check if file exists
	if _, err := os.Stat(filepath); os.IsNotExist(err) {
		return fmt.Errorf("session not found: %s", sessionID)
	}

	// Delete file
	if err := os.Remove(filepath); err != nil {
		return fmt.Errorf("failed to delete session file: %w", err)
	}

	return nil
}

// ListSessions returns a list of all session summaries
func (m *Manager) ListSessions() ([]*types.SessionSummary, error) {
	// Read session directory
	entries, err := os.ReadDir(m.sessionDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read session directory: %w", err)
	}

	var summaries []*types.SessionSummary

	// Process each session file
	for _, entry := range entries {
		if entry.IsDir() || !strings.HasSuffix(entry.Name(), ".json") {
			continue
		}

		// Extract session ID from filename
		sessionID := strings.TrimSuffix(entry.Name(), ".json")

		// Load session summary
		summary, err := m.loadSessionSummary(sessionID)
		if err != nil {
			// Log warning but continue with other sessions
			fmt.Printf("Warning: failed to load session %s: %v\n", sessionID, err)
			continue
		}

		summaries = append(summaries, summary)
	}

	// Sort by last activity (most recent first)
	sort.Slice(summaries, func(i, j int) bool {
		return summaries[i].LastActivity.After(summaries[j].LastActivity)
	})

	return summaries, nil
}

// loadSessionSummary loads only the summary information from a session file
func (m *Manager) loadSessionSummary(sessionID string) (*types.SessionSummary, error) {
	// For now, load the full session and convert to summary
	// In a production system, you might want to store summaries separately
	session, err := m.LoadSession(sessionID)
	if err != nil {
		return nil, err
	}

	return session.ToSummary(), nil
}

// GetSessionCount returns the number of stored sessions
func (m *Manager) GetSessionCount() (int, error) {
	entries, err := os.ReadDir(m.sessionDir)
	if err != nil {
		return 0, fmt.Errorf("failed to read session directory: %w", err)
	}

	count := 0
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".json") {
			count++
		}
	}

	return count, nil
}

// cleanupOldSessions removes old sessions if the limit is exceeded
func (m *Manager) cleanupOldSessions() error {
	if m.maxSessions <= 0 {
		return nil // No limit set
	}

	summaries, err := m.ListSessions()
	if err != nil {
		return err
	}

	if len(summaries) <= m.maxSessions {
		return nil // Under limit
	}

	// Calculate how many sessions to delete
	toDelete := len(summaries) - m.maxSessions

	// Delete oldest sessions (summaries are sorted by last activity, newest first)
	for i := len(summaries) - toDelete; i < len(summaries); i++ {
		if err := m.DeleteSession(summaries[i].ID); err != nil {
			return fmt.Errorf("failed to delete old session %s: %w", summaries[i].ID, err)
		}
	}

	return nil
}

// ArchiveSession marks a session as archived
func (m *Manager) ArchiveSession(sessionID string) error {
	session, err := m.LoadSession(sessionID)
	if err != nil {
		return err
	}

	session.SetStatus(types.StatusArchived)
	return m.SaveSession(session)
}

// RestoreSession marks an archived session as active
func (m *Manager) RestoreSession(sessionID string) error {
	session, err := m.LoadSession(sessionID)
	if err != nil {
		return err
	}

	session.SetStatus(types.StatusActive)
	return m.SaveSession(session)
}

// SearchSessions searches for sessions by name or content
func (m *Manager) SearchSessions(query string) ([]*types.SessionSummary, error) {
	summaries, err := m.ListSessions()
	if err != nil {
		return nil, err
	}

	query = strings.ToLower(query)
	var results []*types.SessionSummary

	for _, summary := range summaries {
		// Search in name and description
		if strings.Contains(strings.ToLower(summary.Name), query) ||
		   strings.Contains(strings.ToLower(summary.Description), query) {
			results = append(results, summary)
		}
	}

	return results, nil
}

// ExportSession exports a session to a file
func (m *Manager) ExportSession(sessionID, exportPath string) error {
	session, err := m.LoadSession(sessionID)
	if err != nil {
		return err
	}

	// Marshal session to JSON with indentation
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session for export: %w", err)
	}

	// Write to export file
	if err := os.WriteFile(exportPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write export file: %w", err)
	}

	return nil
}

// ImportSession imports a session from a file
func (m *Manager) ImportSession(importPath string) (*types.Session, error) {
	// Read import file
	data, err := os.ReadFile(importPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read import file: %w", err)
	}

	// Unmarshal session
	var session types.Session
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("failed to unmarshal imported session: %w", err)
	}

	// Generate new ID to avoid conflicts
	session.ID = generateSessionID()
	session.CreatedAt = time.Now()
	session.UpdatedAt = time.Now()

	// Save imported session
	if err := m.SaveSession(&session); err != nil {
		return nil, fmt.Errorf("failed to save imported session: %w", err)
	}

	return &session, nil
}

// GetSessionDirectory returns the session directory path
func (m *Manager) GetSessionDirectory() string {
	return m.sessionDir
}

// SetMaxSessions updates the maximum number of sessions to keep
func (m *Manager) SetMaxSessions(max int) {
	m.maxSessions = max
}

// GetMaxSessions returns the maximum number of sessions to keep
func (m *Manager) GetMaxSessions() int {
	return m.maxSessions
}

// generateSessionID generates a unique session ID
func generateSessionID() string {
	return "session-" + time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString generates a random string of the given length
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
