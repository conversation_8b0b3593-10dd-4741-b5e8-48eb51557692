package types

import (
	"time"
)

// Session represents a conversation session
type Session struct {
	ID              string            `json:"id"`
	Name            string            `json:"name"`
	Description     string            `json:"description,omitempty"`
	Messages        []*Message        `json:"messages"`
	CreatedAt       time.Time         `json:"created_at"`
	UpdatedAt       time.Time         `json:"updated_at"`
	LastActivity    time.Time         `json:"last_activity"`
	WorkingDir      string            `json:"working_directory"`
	Environment     map[string]string `json:"environment,omitempty"`
	LLMConfig       LLMConfig         `json:"llm_config"`
	TokenUsage      SessionTokenUsage `json:"token_usage"`
	CommandHistory  []string          `json:"command_history"`
	Status          SessionStatus     `json:"status"`
	Tags            []string          `json:"tags,omitempty"`
	Metadata        map[string]any    `json:"metadata,omitempty"`
}

// SessionStatus represents the current status of a session
type SessionStatus string

const (
	StatusActive    SessionStatus = "active"
	StatusInactive  SessionStatus = "inactive"
	StatusArchived  SessionStatus = "archived"
	StatusError     SessionStatus = "error"
)

// SessionTokenUsage tracks token usage across a session
type SessionTokenUsage struct {
	TotalPromptTokens     int `json:"total_prompt_tokens"`
	TotalCompletionTokens int `json:"total_completion_tokens"`
	TotalTokens           int `json:"total_tokens"`
	RequestCount          int `json:"request_count"`
	LastUpdated           time.Time `json:"last_updated"`
}

// SessionSummary provides a lightweight view of a session
type SessionSummary struct {
	ID           string        `json:"id"`
	Name         string        `json:"name"`
	Description  string        `json:"description,omitempty"`
	MessageCount int           `json:"message_count"`
	CreatedAt    time.Time     `json:"created_at"`
	UpdatedAt    time.Time     `json:"updated_at"`
	LastActivity time.Time     `json:"last_activity"`
	Status       SessionStatus `json:"status"`
	Tags         []string      `json:"tags,omitempty"`
	TokenUsage   SessionTokenUsage `json:"token_usage"`
}

// NewSession creates a new session with the given parameters
func NewSession(name, description string, llmConfig LLMConfig) *Session {
	now := time.Now()
	return &Session{
		ID:             generateSessionID(),
		Name:           name,
		Description:    description,
		Messages:       make([]*Message, 0),
		CreatedAt:      now,
		UpdatedAt:      now,
		LastActivity:   now,
		WorkingDir:     getCurrentWorkingDir(),
		Environment:    make(map[string]string),
		LLMConfig:      llmConfig,
		TokenUsage:     SessionTokenUsage{LastUpdated: now},
		CommandHistory: make([]string, 0),
		Status:         StatusActive,
		Tags:           make([]string, 0),
		Metadata:       make(map[string]any),
	}
}

// AddMessage adds a message to the session
func (s *Session) AddMessage(message *Message) {
	s.Messages = append(s.Messages, message)
	s.UpdatedAt = time.Now()
	s.LastActivity = time.Now()
	
	// Update token usage if available
	if message.TokenUsage != nil {
		s.TokenUsage.TotalPromptTokens += message.TokenUsage.PromptTokens
		s.TokenUsage.TotalCompletionTokens += message.TokenUsage.CompletionTokens
		s.TokenUsage.TotalTokens += message.TokenUsage.TotalTokens
		s.TokenUsage.RequestCount++
		s.TokenUsage.LastUpdated = time.Now()
	}
	
	// Add to command history if it's a command
	if message.IsCommand() {
		s.CommandHistory = append(s.CommandHistory, message.Content)
	}
}

// GetRecentMessages returns the most recent messages up to the specified limit
func (s *Session) GetRecentMessages(limit int) []*Message {
	if limit <= 0 || limit >= len(s.Messages) {
		return s.Messages
	}
	return s.Messages[len(s.Messages)-limit:]
}

// GetMessagesByRole returns all messages from the specified role
func (s *Session) GetMessagesByRole(role MessageRole) []*Message {
	var messages []*Message
	for _, msg := range s.Messages {
		if msg.Role == role {
			messages = append(messages, msg)
		}
	}
	return messages
}

// GetMessagesByType returns all messages of the specified type
func (s *Session) GetMessagesByType(msgType MessageType) []*Message {
	var messages []*Message
	for _, msg := range s.Messages {
		if msg.Type == msgType {
			messages = append(messages, msg)
		}
	}
	return messages
}

// GetCommandHistory returns the command history
func (s *Session) GetCommandHistory() []string {
	return s.CommandHistory
}

// SetStatus updates the session status
func (s *Session) SetStatus(status SessionStatus) {
	s.Status = status
	s.UpdatedAt = time.Now()
}

// AddTag adds a tag to the session
func (s *Session) AddTag(tag string) {
	for _, existingTag := range s.Tags {
		if existingTag == tag {
			return // Tag already exists
		}
	}
	s.Tags = append(s.Tags, tag)
	s.UpdatedAt = time.Now()
}

// RemoveTag removes a tag from the session
func (s *Session) RemoveTag(tag string) {
	for i, existingTag := range s.Tags {
		if existingTag == tag {
			s.Tags = append(s.Tags[:i], s.Tags[i+1:]...)
			s.UpdatedAt = time.Now()
			break
		}
	}
}

// ToSummary converts the session to a summary
func (s *Session) ToSummary() *SessionSummary {
	return &SessionSummary{
		ID:           s.ID,
		Name:         s.Name,
		Description:  s.Description,
		MessageCount: len(s.Messages),
		CreatedAt:    s.CreatedAt,
		UpdatedAt:    s.UpdatedAt,
		LastActivity: s.LastActivity,
		Status:       s.Status,
		Tags:         s.Tags,
		TokenUsage:   s.TokenUsage,
	}
}

// IsActive returns true if the session is active
func (s *Session) IsActive() bool {
	return s.Status == StatusActive
}

// IsArchived returns true if the session is archived
func (s *Session) IsArchived() bool {
	return s.Status == StatusArchived
}

// generateSessionID generates a unique session ID
func generateSessionID() string {
	return "session-" + time.Now().Format("20060102150405") + "-" + randomString(8)
}

// getCurrentWorkingDir returns the current working directory
func getCurrentWorkingDir() string {
	// This would be implemented to get the actual working directory
	return "."
}
