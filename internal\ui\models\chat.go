package models

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/config"
	"arien-ai/internal/llm"
	"arien-ai/internal/session"
	"arien-ai/internal/ui/components"
	"arien-ai/internal/ui/styles"
	"arien-ai/internal/utils"
	"arien-ai/pkg/types"
)

// ChatModel represents the chat interface model
type ChatModel struct {
	// Core components
	input         *components.InputComponent
	history       *components.HistoryComponent
	header        *components.HeaderComponent
	statusBar     *components.StatusBarComponent
	thinking      *components.ThinkingComponent

	// State
	width         int
	height        int
	focused       bool
	isThinking    bool
	inputMode     InputMode
	
	// Dependencies
	session       *session.Manager
	llmClient     llm.Client
	configManager *config.Manager
	contextMgr    *session.ContextManager

	// Chat state
	currentSession *types.Session
	messageBuffer  []types.Message
	
	// UI state
	showHelp      bool
	showStats     bool
	autoScroll    bool
	
	// Styling
	theme         *styles.Theme
}

// ChatMsg represents chat-specific messages
type ChatMsg interface {
	ChatMsg()
}

// NewChatMessage creates a new chat message
type NewChatMessage struct {
	Content string
	Type    types.MessageType
}

func (NewChatMessage) ChatMsg() {}

// ChatResponseMessage represents a response from the LLM
type ChatResponseMessage struct {
	Message *types.Message
	Error   error
}

func (ChatResponseMessage) ChatMsg() {}

// ChatStatusMessage represents status updates
type ChatStatusMessage struct {
	Status string
	Level  string // info, warning, error
}

func (ChatStatusMessage) ChatMsg() {}

// NewChatModel creates a new chat model
func NewChatModel(
	sessionMgr *session.Manager,
	llmClient llm.Client,
	configMgr *config.Manager,
) *ChatModel {
	theme := styles.NewTheme(configMgr.Get().UI.Theme)
	
	return &ChatModel{
		input:         components.NewInputComponent(),
		history:       components.NewHistoryComponent(),
		header:        components.NewHeaderComponent(),
		statusBar:     components.NewStatusBarComponent(),
		thinking:      components.NewThinkingComponent(),
		
		session:       sessionMgr,
		llmClient:     llmClient,
		configManager: configMgr,
		contextMgr:    session.NewContextManager(50, 4000),
		
		focused:       true,
		inputMode:     ModeNormal,
		autoScroll:    true,
		theme:         theme,
	}
}

// Init initializes the chat model
func (m *ChatModel) Init() tea.Cmd {
	// Load or create current session
	currentSession, err := m.session.GetCurrentSession()
	if err != nil {
		// Create new session if none exists
		currentSession = m.session.NewSession("Chat Session")
	}
	m.currentSession = currentSession

	// Initialize components
	var cmds []tea.Cmd
	cmds = append(cmds, m.input.Init())
	cmds = append(cmds, m.history.Init())
	cmds = append(cmds, m.header.Init())
	cmds = append(cmds, m.statusBar.Init())
	cmds = append(cmds, m.thinking.Init())

	// Load session messages into history
	for _, msg := range currentSession.Messages {
		m.history.AddMessage(msg)
	}

	// Update header with session info
	m.updateHeader()

	return tea.Batch(cmds...)
}

// Update handles messages and updates the model
func (m *ChatModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.updateComponentSizes()
		return m, nil

	case tea.KeyMsg:
		return m.handleKeyMsg(msg)

	case NewChatMessage:
		return m.handleNewMessage(msg)

	case ChatResponseMessage:
		return m.handleChatResponse(msg)

	case ChatStatusMessage:
		return m.handleStatusMessage(msg)

	case ThinkingStartMsg:
		m.isThinking = true
		return m, m.thinking.Start(msg.Message)

	case ThinkingStopMsg:
		m.isThinking = false
		m.thinking.Stop()
		return m, nil
	}

	// Update components
	var cmd tea.Cmd
	
	m.input, cmd = m.input.Update(msg)
	cmds = append(cmds, cmd)
	
	m.history, cmd = m.history.Update(msg)
	cmds = append(cmds, cmd)
	
	m.header, cmd = m.header.Update(msg)
	cmds = append(cmds, cmd)
	
	m.statusBar, cmd = m.statusBar.Update(msg)
	cmds = append(cmds, cmd)
	
	m.thinking, cmd = m.thinking.Update(msg)
	cmds = append(cmds, cmd)

	return m, tea.Batch(cmds...)
}

// View renders the chat interface
func (m *ChatModel) View() string {
	if m.width == 0 || m.height == 0 {
		return "Loading..."
	}

	var sections []string

	// Header
	sections = append(sections, m.header.View())

	// Main content area
	contentHeight := m.height - 4 // Header + status bar + input
	
	if m.showHelp {
		sections = append(sections, m.renderHelp(contentHeight))
	} else if m.showStats {
		sections = append(sections, m.renderStats(contentHeight))
	} else {
		// Chat history
		historyHeight := contentHeight - 1 // Leave space for thinking animation
		m.history.SetSize(m.width, historyHeight)
		sections = append(sections, m.history.View())
		
		// Thinking animation (if active)
		if m.isThinking {
			sections = append(sections, m.thinking.View())
		}
	}

	// Input area
	sections = append(sections, m.input.View())

	// Status bar
	sections = append(sections, m.statusBar.View())

	return lipgloss.JoinVertical(lipgloss.Left, sections...)
}

// handleKeyMsg handles keyboard input
func (m *ChatModel) handleKeyMsg(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "ctrl+c":
		return m, tea.Quit

	case "enter":
		if m.inputMode == ModeNormal {
			return m.handleSendMessage()
		}

	case "esc":
		m.inputMode = ModeNormal
		m.showHelp = false
		m.showStats = false
		return m, nil

	case "ctrl+h", "f1":
		m.showHelp = !m.showHelp
		return m, nil

	case "ctrl+s":
		m.showStats = !m.showStats
		return m, nil

	case "ctrl+l":
		return m.handleClearHistory()

	case "ctrl+n":
		return m.handleNewSession()

	case "ctrl+r":
		return m.handleReloadSession()

	case "page_up", "page_down", "up", "down":
		// Pass navigation to history component
		var cmd tea.Cmd
		m.history, cmd = m.history.Update(msg)
		return m, cmd
	}

	// Pass other keys to input component
	var cmd tea.Cmd
	m.input, cmd = m.input.Update(msg)
	return m, cmd
}

// handleSendMessage processes sending a message
func (m *ChatModel) handleSendMessage() (tea.Model, tea.Cmd) {
	content := strings.TrimSpace(m.input.Value())
	if content == "" {
		return m, nil
	}

	// Clear input
	m.input.SetValue("")

	// Create user message
	userMsg := types.NewUserMessage(content)
	
	// Add to session and history
	m.currentSession.AddMessage(userMsg)
	m.history.AddMessage(userMsg)

	// Save session
	m.session.SaveSession(m.currentSession)

	// Start thinking animation and send to LLM
	return m, tea.Batch(
		func() tea.Msg { return ThinkingStartMsg{Message: "Processing..."} },
		m.sendToLLM(content),
	)
}

// sendToLLM sends a message to the LLM
func (m *ChatModel) sendToLLM(content string) tea.Cmd {
	return func() tea.Msg {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Build context window
		contextWindow, err := m.contextMgr.BuildContext(m.currentSession)
		if err != nil {
			return ChatResponseMessage{
				Error: fmt.Errorf("failed to build context: %w", err),
			}
		}

		// Get available tools
		tools := llm.GetAllTools()

		// Send to LLM with retry logic
		var response *types.Message
		retryConfig := utils.LLMRetryConfig()
		err = utils.Retry(ctx, retryConfig, func() error {
			response, err = m.llmClient.Chat(ctx, contextWindow.Messages, tools)
			return err
		})

		return ChatResponseMessage{
			Message: response,
			Error:   err,
		}
	}
}

// handleNewMessage processes a new message
func (m *ChatModel) handleNewMessage(msg NewChatMessage) (tea.Model, tea.Cmd) {
	// Create message
	var newMsg *types.Message
	switch msg.Type {
	case types.MessageTypeUser:
		newMsg = types.NewUserMessage(msg.Content)
	case types.MessageTypeSystem:
		newMsg = types.NewSystemMessage(msg.Content)
	default:
		newMsg = types.NewUserMessage(msg.Content)
	}

	// Add to session and history
	m.currentSession.AddMessage(newMsg)
	m.history.AddMessage(newMsg)

	// Save session
	m.session.SaveSession(m.currentSession)

	return m, nil
}

// handleChatResponse processes LLM responses
func (m *ChatModel) handleChatResponse(msg ChatResponseMessage) (tea.Model, tea.Cmd) {
	// Stop thinking animation
	m.isThinking = false
	m.thinking.Stop()

	if msg.Error != nil {
		// Handle error
		errorMsg := types.NewSystemMessage(fmt.Sprintf("Error: %v", msg.Error))
		m.currentSession.AddMessage(errorMsg)
		m.history.AddMessage(errorMsg)
		
		return m, func() tea.Msg {
			return ChatStatusMessage{
				Status: fmt.Sprintf("Error: %v", msg.Error),
				Level:  "error",
			}
		}
	}

	if msg.Message != nil {
		// Add response to session and history
		m.currentSession.AddMessage(msg.Message)
		m.history.AddMessage(msg.Message)

		// Update token usage
		if msg.Message.TokenUsage != nil {
			m.currentSession.TokenUsage.Add(*msg.Message.TokenUsage)
		}

		// Save session
		m.session.SaveSession(m.currentSession)

		// Handle function calls if present
		if msg.Message.FunctionCall != nil || msg.Message.ToolCalls != nil {
			return m, m.handleFunctionCalls(msg.Message)
		}
	}

	return m, func() tea.Msg {
		return ThinkingStopMsg{}
	}
}

// handleFunctionCalls processes function calls from the LLM
func (m *ChatModel) handleFunctionCalls(msg *types.Message) tea.Cmd {
	return func() tea.Msg {
		// This would integrate with the executor to run commands
		// For now, just return a status message
		return ChatStatusMessage{
			Status: "Function call executed",
			Level:  "info",
		}
	}
}

// Helper methods

func (m *ChatModel) updateComponentSizes() {
	if m.width == 0 || m.height == 0 {
		return
	}

	// Update component sizes
	m.input.SetSize(m.width, 3)
	m.header.SetSize(m.width, 2)
	m.statusBar.SetSize(m.width, 1)
	
	// History takes remaining space
	historyHeight := m.height - 6 // Header(2) + Input(3) + Status(1)
	if m.isThinking {
		historyHeight -= 1
	}
	m.history.SetSize(m.width, historyHeight)
}

func (m *ChatModel) updateHeader() {
	config := m.configManager.Get()
	sessionInfo := fmt.Sprintf("Session: %s | Provider: %s | Model: %s",
		m.currentSession.Title,
		config.LLM.Provider,
		config.LLM.Model,
	)
	m.header.SetContent(sessionInfo)
}

func (m *ChatModel) handleClearHistory() (tea.Model, tea.Cmd) {
	m.currentSession.Messages = []*types.Message{}
	m.history.Clear()
	m.session.SaveSession(m.currentSession)
	
	return m, func() tea.Msg {
		return ChatStatusMessage{
			Status: "Chat history cleared",
			Level:  "info",
		}
	}
}

func (m *ChatModel) handleNewSession() (tea.Model, tea.Cmd) {
	newSession := m.session.NewSession("New Chat Session")
	m.currentSession = newSession
	m.history.Clear()
	m.updateHeader()
	
	return m, func() tea.Msg {
		return ChatStatusMessage{
			Status: "New session created",
			Level:  "info",
		}
	}
}

func (m *ChatModel) handleReloadSession() (tea.Model, tea.Cmd) {
	// Reload current session from storage
	reloadedSession, err := m.session.LoadSession(m.currentSession.ID)
	if err != nil {
		return m, func() tea.Msg {
			return ChatStatusMessage{
				Status: fmt.Sprintf("Failed to reload session: %v", err),
				Level:  "error",
			}
		}
	}
	
	m.currentSession = reloadedSession
	m.history.Clear()
	for _, msg := range reloadedSession.Messages {
		m.history.AddMessage(msg)
	}
	
	return m, func() tea.Msg {
		return ChatStatusMessage{
			Status: "Session reloaded",
			Level:  "info",
		}
	}
}

func (m *ChatModel) handleStatusMessage(msg ChatStatusMessage) (tea.Model, tea.Cmd) {
	m.statusBar.SetStatus(msg.Status, msg.Level)
	return m, nil
}

func (m *ChatModel) renderHelp(height int) string {
	helpContent := `
Chat Interface Help

Keyboard Shortcuts:
  Enter          - Send message
  Ctrl+C         - Exit application
  Ctrl+H, F1     - Toggle this help
  Ctrl+S         - Show session statistics
  Ctrl+L         - Clear chat history
  Ctrl+N         - Start new session
  Ctrl+R         - Reload current session
  Page Up/Down   - Scroll through history
  Esc            - Close help/stats

Features:
  - AI-powered conversation with function calling
  - Automatic session saving
  - Context-aware responses
  - Command execution capabilities
  - Session management

Type your message and press Enter to start chatting!
`
	
	style := lipgloss.NewStyle().
		Width(m.width).
		Height(height).
		Padding(1).
		Border(lipgloss.RoundedBorder()).
		BorderForeground(m.theme.BorderColor)
	
	return style.Render(helpContent)
}

func (m *ChatModel) renderStats(height int) string {
	stats := m.contextMgr.GetContextStats(m.currentSession)
	
	statsContent := fmt.Sprintf(`
Session Statistics

Messages: %d total
Tokens: %d total
Context: %d/%d messages (%.1f%% utilization)
Session Duration: %v
Token Usage: Input: %d, Output: %d, Total: %d

Message Types:
  User: %d
  Assistant: %d
  System: %d
  Function: %d
  Tool: %d
`,
		stats.TotalMessages,
		stats.TotalTokens,
		stats.ContextMessages,
		stats.MaxContextSize,
		stats.ContextUtilization*100,
		time.Since(m.currentSession.CreatedAt).Round(time.Minute),
		m.currentSession.TokenUsage.InputTokens,
		m.currentSession.TokenUsage.OutputTokens,
		m.currentSession.TokenUsage.TotalTokens,
		stats.MessagesByType[types.MessageTypeUser],
		stats.MessagesByType[types.MessageTypeAssistant],
		stats.MessagesByType[types.MessageTypeSystem],
		stats.MessagesByType[types.MessageTypeFunction],
		stats.MessagesByType[types.MessageTypeTool],
	)
	
	style := lipgloss.NewStyle().
		Width(m.width).
		Height(height).
		Padding(1).
		Border(lipgloss.RoundedBorder()).
		BorderForeground(m.theme.BorderColor)
	
	return style.Render(statsContent)
}
