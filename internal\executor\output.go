package executor

import (
	"fmt"
	"io"
	"strings"
	"time"
)

// readWithLimit reads from a reader with a size limit
func readWithLimit(reader io.Reader, limit int64) ([]byte, error) {
	limitedReader := io.LimitReader(reader, limit)
	return io.ReadAll(limitedReader)
}

// OutputFormatter handles formatting of command output
type OutputFormatter struct {
	maxLineLength int
	maxLines      int
	showTimestamp bool
}

// NewOutputFormatter creates a new output formatter
func NewOutputFormatter() *OutputFormatter {
	return &OutputFormatter{
		maxLineLength: 1000,
		maxLines:      1000,
		showTimestamp: true,
	}
}

// FormatCommandOutput formats command output for display
func (f *OutputFormatter) FormatCommandOutput(stdout, stderr string, exitCode int, duration time.Duration) string {
	var result strings.Builder

	// Add execution summary
	if f.showTimestamp {
		result.WriteString(fmt.Sprintf("Executed at: %s\n", time.Now().Format("2006-01-02 15:04:05")))
	}
	result.WriteString(fmt.Sprintf("Duration: %v\n", duration))
	result.WriteString(fmt.Sprintf("Exit Code: %d\n", exitCode))
	result.WriteString(strings.Repeat("-", 50) + "\n")

	// Add stdout if present
	if stdout != "" {
		result.WriteString("STDOUT:\n")
		result.WriteString(f.formatOutput(stdout))
		result.WriteString("\n")
	}

	// Add stderr if present
	if stderr != "" {
		result.WriteString("STDERR:\n")
		result.WriteString(f.formatOutput(stderr))
		result.WriteString("\n")
	}

	// Add status
	if exitCode == 0 {
		result.WriteString("✅ Command executed successfully\n")
	} else {
		result.WriteString("❌ Command failed\n")
	}

	return result.String()
}

// formatOutput formats output text with line and length limits
func (f *OutputFormatter) formatOutput(output string) string {
	lines := strings.Split(output, "\n")
	
	// Limit number of lines
	if len(lines) > f.maxLines {
		lines = lines[:f.maxLines]
		lines = append(lines, fmt.Sprintf("... (truncated, %d more lines)", len(strings.Split(output, "\n"))-f.maxLines))
	}

	// Limit line length
	for i, line := range lines {
		if len(line) > f.maxLineLength {
			lines[i] = line[:f.maxLineLength] + "... (truncated)"
		}
	}

	return strings.Join(lines, "\n")
}

// FormatError formats an error message
func (f *OutputFormatter) FormatError(err error) string {
	return fmt.Sprintf("❌ Error: %v", err)
}

// FormatSuccess formats a success message
func (f *OutputFormatter) FormatSuccess(message string) string {
	return fmt.Sprintf("✅ %s", message)
}

// FormatWarning formats a warning message
func (f *OutputFormatter) FormatWarning(message string) string {
	return fmt.Sprintf("⚠️  %s", message)
}

// FormatInfo formats an info message
func (f *OutputFormatter) FormatInfo(message string) string {
	return fmt.Sprintf("ℹ️  %s", message)
}

// SetMaxLineLength sets the maximum line length
func (f *OutputFormatter) SetMaxLineLength(length int) {
	f.maxLineLength = length
}

// SetMaxLines sets the maximum number of lines
func (f *OutputFormatter) SetMaxLines(lines int) {
	f.maxLines = lines
}

// SetShowTimestamp sets whether to show timestamps
func (f *OutputFormatter) SetShowTimestamp(show bool) {
	f.showTimestamp = show
}

// TruncateOutput truncates output to specified limits
func TruncateOutput(output string, maxLines, maxLineLength int) string {
	lines := strings.Split(output, "\n")
	
	// Limit number of lines
	if len(lines) > maxLines {
		lines = lines[:maxLines]
		lines = append(lines, "... (output truncated)")
	}

	// Limit line length
	for i, line := range lines {
		if len(line) > maxLineLength {
			lines[i] = line[:maxLineLength] + "..."
		}
	}

	return strings.Join(lines, "\n")
}

// SanitizeOutput removes potentially sensitive information from output
func SanitizeOutput(output string) string {
	// Remove common sensitive patterns
	sensitivePatterns := []string{
		// API keys and tokens
		`(?i)(api[_-]?key|token|secret)["\s]*[:=]["\s]*[a-zA-Z0-9_-]+`,
		// Passwords
		`(?i)(password|passwd|pwd)["\s]*[:=]["\s]*[^\s"]+`,
		// URLs with credentials
		`https?://[^:]+:[^@]+@[^\s]+`,
		// Private keys
		`-----BEGIN [A-Z ]+PRIVATE KEY-----[\s\S]*?-----END [A-Z ]+PRIVATE KEY-----`,
	}

	result := output
	for _, pattern := range sensitivePatterns {
		// Simple replacement - in production, use proper regex
		if strings.Contains(strings.ToLower(result), "password") ||
		   strings.Contains(strings.ToLower(result), "token") ||
		   strings.Contains(strings.ToLower(result), "key") {
			result = strings.ReplaceAll(result, result, "[REDACTED]")
			break
		}
	}

	return result
}

// ColorizeOutput adds color codes to output based on content
func ColorizeOutput(output string) string {
	lines := strings.Split(output, "\n")
	
	for i, line := range lines {
		lowerLine := strings.ToLower(line)
		
		// Color error lines red
		if strings.Contains(lowerLine, "error") || strings.Contains(lowerLine, "failed") {
			lines[i] = fmt.Sprintf("\033[31m%s\033[0m", line) // Red
		}
		// Color warning lines yellow
		if strings.Contains(lowerLine, "warning") || strings.Contains(lowerLine, "warn") {
			lines[i] = fmt.Sprintf("\033[33m%s\033[0m", line) // Yellow
		}
		// Color success lines green
		if strings.Contains(lowerLine, "success") || strings.Contains(lowerLine, "completed") {
			lines[i] = fmt.Sprintf("\033[32m%s\033[0m", line) // Green
		}
	}

	return strings.Join(lines, "\n")
}

// ExtractImportantInfo extracts important information from command output
func ExtractImportantInfo(output string) []string {
	var important []string
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		lowerLine := strings.ToLower(line)
		
		// Extract error messages
		if strings.Contains(lowerLine, "error") || strings.Contains(lowerLine, "failed") {
			important = append(important, line)
		}
		
		// Extract warnings
		if strings.Contains(lowerLine, "warning") || strings.Contains(lowerLine, "warn") {
			important = append(important, line)
		}
		
		// Extract file paths
		if strings.Contains(line, "/") || strings.Contains(line, "\\") {
			if len(line) < 200 { // Avoid very long lines
				important = append(important, line)
			}
		}
		
		// Extract version information
		if strings.Contains(lowerLine, "version") {
			important = append(important, line)
		}
	}

	return important
}
