package models

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/bubbles/list"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/config"
	"arien-ai/internal/session"
	"arien-ai/internal/ui/styles"
	"arien-ai/pkg/types"
)

// CommandsModel represents the slash commands interface
type CommandsModel struct {
	// UI components
	list          list.Model
	width         int
	height        int
	visible       bool
	
	// State
	selectedIndex int
	filterText    string
	
	// Dependencies
	configManager *config.Manager
	sessionMgr    *session.Manager
	theme         *styles.Theme
	
	// Commands
	commands      []Command
	filteredCmds  []Command
}

// Command represents a slash command
type Command struct {
	Name        string
	Description string
	Usage       string
	Category    string
	Handler     func(*CommandsModel, []string) tea.Cmd
}

// CommandMsg represents command-related messages
type CommandMsg interface {
	CommandMsg()
}

// CommandExecutedMsg indicates a command was executed
type CommandExecutedMsg struct {
	Command string
	Args    []string
	Result  string
	Error   error
}

func (CommandExecutedMsg) CommandMsg() {}

// CommandSelectedMsg indicates a command was selected
type CommandSelectedMsg struct {
	Command Command
	Args    []string
}

func (CommandSelectedMsg) CommandMsg() {}

// NewCommandsModel creates a new commands model
func NewCommandsModel(configMgr *config.Manager, sessionMgr *session.Manager) *CommandsModel {
	theme := styles.NewTheme(configMgr.Get().UI.Theme)
	
	m := &CommandsModel{
		configManager: configMgr,
		sessionMgr:    sessionMgr,
		theme:         theme,
		commands:      createCommands(),
	}
	
	m.filteredCmds = m.commands
	m.setupList()
	
	return m
}

// Init initializes the commands model
func (m *CommandsModel) Init() tea.Cmd {
	return nil
}

// Update handles messages and updates the model
func (m *CommandsModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.updateListSize()
		return m, nil

	case tea.KeyMsg:
		return m.handleKeyMsg(msg)
	}

	var cmd tea.Cmd
	m.list, cmd = m.list.Update(msg)
	return m, cmd
}

// View renders the commands interface
func (m *CommandsModel) View() string {
	if !m.visible {
		return ""
	}

	style := lipgloss.NewStyle().
		Width(m.width).
		Height(m.height).
		Border(lipgloss.RoundedBorder()).
		BorderForeground(m.theme.BorderColor).
		Padding(1)

	content := m.list.View()
	
	if m.filterText != "" {
		header := fmt.Sprintf("Commands (filter: %s)", m.filterText)
		content = lipgloss.JoinVertical(lipgloss.Left, header, content)
	}

	return style.Render(content)
}

// Show makes the commands interface visible
func (m *CommandsModel) Show() {
	m.visible = true
	m.filterText = ""
	m.filteredCmds = m.commands
	m.updateList()
}

// Hide makes the commands interface invisible
func (m *CommandsModel) Hide() {
	m.visible = false
	m.filterText = ""
}

// IsVisible returns whether the commands interface is visible
func (m *CommandsModel) IsVisible() bool {
	return m.visible
}

// handleKeyMsg handles keyboard input
func (m *CommandsModel) handleKeyMsg(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "esc":
		m.Hide()
		return m, nil

	case "enter":
		return m.handleCommandSelection()

	case "ctrl+c":
		return m, tea.Quit

	case "/":
		// Start filtering
		return m, nil

	default:
		// Handle filtering
		if len(msg.String()) == 1 && msg.String() != " " {
			m.filterText += msg.String()
			m.filterCommands()
			return m, nil
		}
		
		if msg.String() == "backspace" && len(m.filterText) > 0 {
			m.filterText = m.filterText[:len(m.filterText)-1]
			m.filterCommands()
			return m, nil
		}
	}

	var cmd tea.Cmd
	m.list, cmd = m.list.Update(msg)
	return m, cmd
}

// handleCommandSelection handles when a command is selected
func (m *CommandsModel) handleCommandSelection() (tea.Model, tea.Cmd) {
	if len(m.filteredCmds) == 0 {
		return m, nil
	}

	selectedItem := m.list.SelectedItem()
	if selectedItem == nil {
		return m, nil
	}

	// Find the command
	var selectedCmd Command
	for _, cmd := range m.filteredCmds {
		if cmd.Name == selectedItem.(commandItem).Name {
			selectedCmd = cmd
			break
		}
	}

	m.Hide()

	// Execute command
	if selectedCmd.Handler != nil {
		return m, selectedCmd.Handler(m, []string{})
	}

	return m, func() tea.Msg {
		return CommandSelectedMsg{
			Command: selectedCmd,
			Args:    []string{},
		}
	}
}

// filterCommands filters commands based on the filter text
func (m *CommandsModel) filterCommands() {
	if m.filterText == "" {
		m.filteredCmds = m.commands
	} else {
		m.filteredCmds = []Command{}
		filter := strings.ToLower(m.filterText)
		
		for _, cmd := range m.commands {
			if strings.Contains(strings.ToLower(cmd.Name), filter) ||
				strings.Contains(strings.ToLower(cmd.Description), filter) ||
				strings.Contains(strings.ToLower(cmd.Category), filter) {
				m.filteredCmds = append(m.filteredCmds, cmd)
			}
		}
	}
	
	m.updateList()
}

// setupList initializes the list component
func (m *CommandsModel) setupList() {
	items := make([]list.Item, len(m.filteredCmds))
	for i, cmd := range m.filteredCmds {
		items[i] = commandItem{
			Name:        cmd.Name,
			Description: cmd.Description,
			Category:    cmd.Category,
		}
	}

	delegate := list.NewDefaultDelegate()
	delegate.Styles.SelectedTitle = delegate.Styles.SelectedTitle.
		Foreground(m.theme.AccentColor).
		BorderLeftForeground(m.theme.AccentColor)
	
	m.list = list.New(items, delegate, 0, 0)
	m.list.Title = "Available Commands"
	m.list.SetShowStatusBar(false)
	m.list.SetFilteringEnabled(false)
}

// updateList updates the list with filtered commands
func (m *CommandsModel) updateList() {
	items := make([]list.Item, len(m.filteredCmds))
	for i, cmd := range m.filteredCmds {
		items[i] = commandItem{
			Name:        cmd.Name,
			Description: cmd.Description,
			Category:    cmd.Category,
		}
	}
	m.list.SetItems(items)
}

// updateListSize updates the list size
func (m *CommandsModel) updateListSize() {
	if m.width > 0 && m.height > 0 {
		m.list.SetSize(m.width-4, m.height-4) // Account for border and padding
	}
}

// commandItem implements list.Item for commands
type commandItem struct {
	Name        string
	Description string
	Category    string
}

func (i commandItem) FilterValue() string {
	return i.Name + " " + i.Description + " " + i.Category
}

func (i commandItem) Title() string {
	return fmt.Sprintf("/%s", i.Name)
}

func (i commandItem) Description() string {
	return fmt.Sprintf("[%s] %s", i.Category, i.Description)
}

// createCommands creates the available slash commands
func createCommands() []Command {
	return []Command{
		{
			Name:        "help",
			Description: "Show available commands and help",
			Usage:       "/help [command]",
			Category:    "General",
			Handler:     handleHelpCmd,
		},
		{
			Name:        "clear",
			Description: "Clear conversation history",
			Usage:       "/clear",
			Category:    "Session",
			Handler:     handleClearCmd,
		},
		{
			Name:        "save",
			Description: "Save current session",
			Usage:       "/save [name]",
			Category:    "Session",
			Handler:     handleSaveCmd,
		},
		{
			Name:        "load",
			Description: "Load a saved session",
			Usage:       "/load <session_id>",
			Category:    "Session",
			Handler:     handleLoadCmd,
		},
		{
			Name:        "sessions",
			Description: "List all saved sessions",
			Usage:       "/sessions",
			Category:    "Session",
			Handler:     handleSessionsCmd,
		},
		{
			Name:        "delete",
			Description: "Delete a saved session",
			Usage:       "/delete <session_id>",
			Category:    "Session",
			Handler:     handleDeleteCmd,
		},
		{
			Name:        "export",
			Description: "Export session to file",
			Usage:       "/export [filename]",
			Category:    "Session",
			Handler:     handleExportCmd,
		},
		{
			Name:        "import",
			Description: "Import session from file",
			Usage:       "/import <filename>",
			Category:    "Session",
			Handler:     handleImportCmd,
		},
		{
			Name:        "model",
			Description: "Change or show current model",
			Usage:       "/model [model_name]",
			Category:    "Config",
			Handler:     handleModelCmd,
		},
		{
			Name:        "provider",
			Description: "Change or show current provider",
			Usage:       "/provider [provider_name]",
			Category:    "Config",
			Handler:     handleProviderCmd,
		},
		{
			Name:        "config",
			Description: "Show current configuration",
			Usage:       "/config",
			Category:    "Config",
			Handler:     handleConfigCmd,
		},
		{
			Name:        "theme",
			Description: "Change UI theme",
			Usage:       "/theme [theme_name]",
			Category:    "UI",
			Handler:     handleThemeCmd,
		},
		{
			Name:        "stats",
			Description: "Show session statistics",
			Usage:       "/stats",
			Category:    "Info",
			Handler:     handleStatsCmd,
		},
		{
			Name:        "tokens",
			Description: "Show token usage information",
			Usage:       "/tokens",
			Category:    "Info",
			Handler:     handleTokensCmd,
		},
		{
			Name:        "context",
			Description: "Show context window information",
			Usage:       "/context",
			Category:    "Info",
			Handler:     handleContextCmd,
		},
		{
			Name:        "quit",
			Description: "Exit the application",
			Usage:       "/quit",
			Category:    "General",
			Handler:     handleQuitCmd,
		},
		{
			Name:        "exit",
			Description: "Exit the application",
			Usage:       "/exit",
			Category:    "General",
			Handler:     handleQuitCmd,
		},
	}
}

// Command handlers

func handleHelpCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		var helpText strings.Builder
		helpText.WriteString("Available Commands:\n\n")
		
		categories := make(map[string][]Command)
		for _, cmd := range m.commands {
			categories[cmd.Category] = append(categories[cmd.Category], cmd)
		}
		
		for category, cmds := range categories {
			helpText.WriteString(fmt.Sprintf("%s:\n", category))
			for _, cmd := range cmds {
				helpText.WriteString(fmt.Sprintf("  /%s - %s\n", cmd.Name, cmd.Description))
			}
			helpText.WriteString("\n")
		}
		
		return CommandExecutedMsg{
			Command: "help",
			Args:    args,
			Result:  helpText.String(),
		}
	}
}

func handleClearCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		return CommandExecutedMsg{
			Command: "clear",
			Args:    args,
			Result:  "Chat history cleared",
		}
	}
}

func handleSaveCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		return CommandExecutedMsg{
			Command: "save",
			Args:    args,
			Result:  "Session saved",
		}
	}
}

func handleLoadCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		if len(args) == 0 {
			return CommandExecutedMsg{
				Command: "load",
				Args:    args,
				Error:   fmt.Errorf("session ID required"),
			}
		}
		
		return CommandExecutedMsg{
			Command: "load",
			Args:    args,
			Result:  fmt.Sprintf("Loaded session: %s", args[0]),
		}
	}
}

func handleSessionsCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		sessions, err := m.sessionMgr.ListSessions()
		if err != nil {
			return CommandExecutedMsg{
				Command: "sessions",
				Args:    args,
				Error:   err,
			}
		}
		
		var result strings.Builder
		result.WriteString("Saved Sessions:\n\n")
		
		for _, session := range sessions {
			result.WriteString(fmt.Sprintf("ID: %s\n", session.ID))
			result.WriteString(fmt.Sprintf("Title: %s\n", session.Title))
			result.WriteString(fmt.Sprintf("Messages: %d\n", session.MessageCount))
			result.WriteString(fmt.Sprintf("Modified: %s\n\n", 
				session.LastModified.Format("2006-01-02 15:04:05")))
		}
		
		return CommandExecutedMsg{
			Command: "sessions",
			Args:    args,
			Result:  result.String(),
		}
	}
}

func handleDeleteCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		if len(args) == 0 {
			return CommandExecutedMsg{
				Command: "delete",
				Args:    args,
				Error:   fmt.Errorf("session ID required"),
			}
		}
		
		return CommandExecutedMsg{
			Command: "delete",
			Args:    args,
			Result:  fmt.Sprintf("Deleted session: %s", args[0]),
		}
	}
}

func handleExportCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		filename := "session_export.json"
		if len(args) > 0 {
			filename = args[0]
		}
		
		return CommandExecutedMsg{
			Command: "export",
			Args:    args,
			Result:  fmt.Sprintf("Session exported to: %s", filename),
		}
	}
}

func handleImportCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		if len(args) == 0 {
			return CommandExecutedMsg{
				Command: "import",
				Args:    args,
				Error:   fmt.Errorf("filename required"),
			}
		}
		
		return CommandExecutedMsg{
			Command: "import",
			Args:    args,
			Result:  fmt.Sprintf("Session imported from: %s", args[0]),
		}
	}
}

func handleModelCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		config := m.configManager.Get()
		
		if len(args) == 0 {
			return CommandExecutedMsg{
				Command: "model",
				Args:    args,
				Result:  fmt.Sprintf("Current model: %s", config.LLM.Model),
			}
		}
		
		return CommandExecutedMsg{
			Command: "model",
			Args:    args,
			Result:  fmt.Sprintf("Model changed to: %s", args[0]),
		}
	}
}

func handleProviderCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		config := m.configManager.Get()
		
		if len(args) == 0 {
			return CommandExecutedMsg{
				Command: "provider",
				Args:    args,
				Result:  fmt.Sprintf("Current provider: %s", config.LLM.Provider),
			}
		}
		
		return CommandExecutedMsg{
			Command: "provider",
			Args:    args,
			Result:  fmt.Sprintf("Provider changed to: %s", args[0]),
		}
	}
}

func handleConfigCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		config := m.configManager.Get()
		
		result := fmt.Sprintf(`Current Configuration:

LLM:
  Provider: %s
  Model: %s
  Temperature: %.2f
  Max Tokens: %d

UI:
  Theme: %s
  Show Timestamps: %t
  Animation Speed: %d

Session:
  Auto Save: %t
  Max Sessions: %d
  Context Window: %d
`,
			config.LLM.Provider,
			config.LLM.Model,
			config.LLM.Temperature,
			config.LLM.MaxTokens,
			config.UI.Theme,
			config.UI.ShowTimestamps,
			config.UI.AnimationSpeed,
			config.Session.AutoSave,
			config.Session.MaxSessions,
			config.Session.ContextWindow,
		)
		
		return CommandExecutedMsg{
			Command: "config",
			Args:    args,
			Result:  result,
		}
	}
}

func handleThemeCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		if len(args) == 0 {
			return CommandExecutedMsg{
				Command: "theme",
				Args:    args,
				Result:  "Available themes: default, dark, light",
			}
		}
		
		return CommandExecutedMsg{
			Command: "theme",
			Args:    args,
			Result:  fmt.Sprintf("Theme changed to: %s", args[0]),
		}
	}
}

func handleStatsCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		return CommandExecutedMsg{
			Command: "stats",
			Args:    args,
			Result:  "Session statistics displayed",
		}
	}
}

func handleTokensCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		return CommandExecutedMsg{
			Command: "tokens",
			Args:    args,
			Result:  "Token usage information displayed",
		}
	}
}

func handleContextCmd(m *CommandsModel, args []string) tea.Cmd {
	return func() tea.Msg {
		return CommandExecutedMsg{
			Command: "context",
			Args:    args,
			Result:  "Context window information displayed",
		}
	}
}

func handleQuitCmd(m *CommandsModel, args []string) tea.Cmd {
	return tea.Quit
}
